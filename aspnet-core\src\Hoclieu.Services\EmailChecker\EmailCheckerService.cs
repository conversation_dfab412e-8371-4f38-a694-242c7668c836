using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Hoclieu.EmailCheckers
{
    public class EmailCheckerService
    {
        private HttpClient _httpClient;

        public EmailCheckerService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<bool> ValidateEmail(string email)
        {
            var emailDomainWhiteList = new Dictionary<string, bool>()
            {
                { "macmillaneducation.com", true },
                { "bee-tech.ai", true },
                { "saokhue.io", true },
                { "hoclieu.vn", true },
                { "gmail.com", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true },
                { "<EMAIL>", true }
            };
            var emailDomain = email.Split('@')[1];

            if (emailDomainWhiteList.ContainsKey(emailDomain) || emailDomainWhiteList.ContainsKey(email))
            {
                return true;
            }

            try
            {
                HttpResponseMessage response = await _httpClient.GetAsync($"{email}/verification");
                response.EnsureSuccessStatusCode();
                string responseBody = await response.Content.ReadAsStringAsync();
                return (responseBody.Contains("\"deliverable\":true")
                        || responseBody.Contains("\"catch_all\":true")
                        || responseBody
                            .Contains(
                                "554 Blocked") //allow domain with mx check 554 <NAME_EMAIL>
                    );
            }
            catch (System.Exception)
            {
                return false;
            }
        }
    }
}
