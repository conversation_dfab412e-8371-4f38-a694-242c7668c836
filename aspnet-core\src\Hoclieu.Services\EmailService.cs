using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using Hangfire;
using Hoclieu.Checkpoints;
using Hoclieu.Core.Enums;
using Hoclieu.Core.Helpers;
using Hoclieu.EmailCheckers;
using Hoclieu.Settings;
using MailKit.Net.Smtp;
using Microsoft.Extensions.Options;
using MimeKit;

namespace Hoclieu.Services
{
    using System.Threading.Tasks;

    public class EmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly AppSettings _appSettings;
        private readonly EmailCheckerService _emailCheckerService;

        public EmailService(IOptions<EmailSettings> emailSettings, IOptions<AppSettings> appSettings,
            EmailCheckerService emailCheckerService)
        {
            _emailSettings = emailSettings.Value;
            _appSettings = appSettings.Value;
            _emailCheckerService = emailCheckerService;
        }

        public ValidateMailState ValidateEmail(string email)
        {
            var emailDomainWhiteList = new Dictionary<string, bool>(){
                {"macmillaneducation.com", true},
            };
            var emailDomain = email.Split('@')[1];

            if (emailDomainWhiteList.ContainsKey(emailDomain))
            {
                return ValidateMailState.Ok;
            }

            try
            {
                TcpClient tcpClient = new TcpClient();
                var result = tcpClient.BeginConnect("gmail-smtp-in.l.google.com", 25, null, null);
                var success = result.AsyncWaitHandle.WaitOne(TimeSpan.FromSeconds(5));

                if (!success)
                {
                    tcpClient.Close();
                    return ValidateMailState.Exception;
                }

                string CRLF = "\r\n";
                byte[] dataBuffer;
                string responseString;
                NetworkStream netStream = tcpClient.GetStream();
                StreamReader reader = new StreamReader(netStream);
                responseString = reader.ReadLine();

                dataBuffer = Encoding.ASCII.GetBytes("HELO Hi" + CRLF);
                netStream.Write(dataBuffer, 0, dataBuffer.Length);
                responseString = reader.ReadLine();
                dataBuffer = Encoding.ASCII.GetBytes($"MAIL FROM:<{_emailSettings.EmailAddress}>" + CRLF);
                netStream.Write(dataBuffer, 0, dataBuffer.Length);
                responseString = reader.ReadLine();

                dataBuffer = Encoding.ASCII.GetBytes($"RCPT TO:<{email}>" + CRLF);
                netStream.Write(dataBuffer, 0, dataBuffer.Length);
                responseString = reader.ReadLine();
                if (responseString.StartsWith("550"))
                {
                    return ValidateMailState.Failed;
                }

                dataBuffer = Encoding.ASCII.GetBytes("QUITE" + CRLF);
                netStream.Write(dataBuffer, 0, dataBuffer.Length);
                tcpClient.Close();
                return ValidateMailState.Ok;
            }
            catch (Exception ex)
            {
                return ValidateMailState.Exception;
            }
        }

        // Nếu Job lỗi sẽ thự hiện thêm 2 lần nữa
        [AutomaticRetry(Attempts = 2)]
        public void SendEmail(string name, string email, string subject, string content, params string[] ccAddressesTxt)
        {
            if (email.IsValidEmail())
            {
                // var mailIsValid = ValidateEmail(email);
                // if (mailIsValid == ValidateMailState.Ok)
                bool mailIsValid = _emailCheckerService.ValidateEmail(email).Result;
                if (mailIsValid)
                {
                    MailboxAddress fromAddress = new MailboxAddress(_emailSettings.Name, _emailSettings.EmailAddress);
                    MailboxAddress toAddress = new MailboxAddress(name, email);

                    BodyBuilder builder = new BodyBuilder();
                    builder.HtmlBody = content;

                    MimeMessage message = new MimeMessage()
                    {
                        Subject = subject,
                        Body = builder.ToMessageBody()
                    };

                    message.From.Add(fromAddress);
                    message.To.Add(toAddress);

                    if (ccAddressesTxt.Length > 0)
                    {
                        var ccAddresses = ccAddressesTxt
                            .Select(ccAddress => new MailboxAddress(ccAddress, ccAddress));
                        message.Cc.AddRange(ccAddresses);
                    }

                    if (!string.IsNullOrEmpty(_emailSettings.ReplyEmailAddress))
                    {
                        MailboxAddress replyToAddress =
                            new MailboxAddress(_emailSettings.Name, _emailSettings.ReplyEmailAddress);
                        message.ReplyTo.Add(replyToAddress);
                    }

                    SmtpClient client = new SmtpClient();
                    client.Connect(_emailSettings.SMTPServer, _emailSettings.SMTPPort, _emailSettings.UseSsl);
                    client.Authenticate(_emailSettings.UserName, _emailSettings.Password);

                    client.Send(message);
                    client.Disconnect(true);
                    client.Dispose();
                }
            }
        }

        public void SendRegisterInvitationEmail(string classroomName, string email)
        {
            var mailTitle = "Lời mời tham gia lớp học";
            var mailContent = $@"<h1 style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif"">

                <h1> Lời mời tham gia lớp học </h1>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Xin chào <b>{email}</b>,
                    <br></br>
                    Bạn nhận được một lời mời tham gia vào lớp học <b>{classroomName}</b>.
                    Bấm vào nút dưới đây để đăng ký và tham gia lớp học </p>
                <a style=""
                    color: #ffffff;
                    text-decoration: none;
                    font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
                    font-weight: 600;
                    padding: 12px 16px 12px 16px;
                    text-align: left;
                    line-height: 1;
                    font-size: 16px;
                    display: inline-block;
                    border: 0 solid #0078d4;
                    background: #0078d4;
                    border-radius: 2px;""
                href=""{_appSettings.ClientURL}/auth/registration"">Đồng ý tham gia</a>";

            BackgroundJob.Enqueue(() => SendEmail(
                email,
                email,
                mailTitle,
                mailContent));
        }

        public void SendMailOrderSuccess(string email, string name, int count)
        {
            var mailTitle = "[Hoclieu.vn] Thông báo mua thẻ thành công";
            // nếu ngày hiện tại nhỏ hơn 30 tháng 7 thì trả về 30 tháng 7 năm nay
            // nếu ngày hiện tại lớn hơn 30 tháng 7 thì trả về 30 tháng 7 năm sau
            // ngày trả về theo định dạng DD/MM/YYYY
            var timeExpiredClt = DateTime.Now.Day < 30 && DateTime.Now.Month <= 7
                ? new DateTime(DateTime.Now.Year, 7, 30).ToString("dd/MM/yyyy")
                : new DateTime(DateTime.Now.Year + 1, 7, 30).ToString("dd/MM/yyyy");
            var mailContent = $@"
              <div style=""
                        font-family: Arial, Helvetica, sans-serif;
                        background-image: url(https://img.hoclieu.vn/hoclieu/shared-image/bg.png);
                        padding: 10px 0;"">
                    <div
                      class=""page""
                      style=""
                        margin: auto;
                        width: 595px;
                        height: 774px;
                        text-align: justify;
                        background-image: url(https://img.hoclieu.vn/hoclieu/shared-image/z3286698714732_f6944a67e54fc066788d7b28ef871e85.jpg);
                        box-shadow: 0px 4px 0px 2px rgba(0, 0, 0, 0.25);
                      ""
                    >
                      <div class=""content"" style=""margin: 0 60px"">
                        <div style=""text-align: center; padding: 40px 0"">
                          <img src=""https://img.hoclieu.vn/hoclieu/shared-image/logo-hoclieu.png"" width=""80"" height=""60"" />
                          <div
                            style=""
                              font-weight: 600;
                              font-size: 24px;
                              line-height: 32px;
                              color: #244e8b;
                              padding-top: 25px;
                            ""
                          >
                            CỔNG LUYỆN THI TỐT NGHIỆP THPT
                          </div>
                          <div
                            style=""
                              font-weight: 700;
                              font-size: 20px;
                              line-height: 28px;
                              color: #ff421d;
                              padding-top: 20px;
                            ""
                          >
                            Thông báo mua thẻ thành công
                          </div>
                        </div>
                        <div
                          style=""
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            color: #244e8b;
                            margin-bottom: 16px;
                          ""
                        >
                          Xin chào
                          <span style=""font-weight: 700; font-style: italic""
                            >{name}</span
                          >,
                        </div>
                        <div
                          style=""
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            color: #244e8b;
                          ""
                        >
                          <div>
                            Bạn đã mua thẻ trên Cổng luyện thi tốt nghiệp THPT thành công:
                          </div>
                          <div style=""padding-left: 5px"">
                            <div>
                              • Số lượng: <span style=""font-weight: 700"">{count}</span> thẻ.
                            </div>
                            <div>
                              • Thời hạn sử dụng:
                              <span style=""font-weight: 700"">{timeExpiredClt}.</span>
                            </div>
                            <div>
                              • Mỗi thẻ có thể sử dụng để kích hoạt 01 môn trên Cổng luyện
                              thi.
                            </div>
                          </div>
                          <div>
                            Chọn môn kích hoạt ngay
                            <a
                              href=""https://hoclieu.vn/user-resource""
                              target=""_blank""
                              style=""font-weight: 700; font-style: italic; color: #244E8B;""
                              >tại đây</a
                            >
                            hoặc ấn nút kích hoạt bên dưới.
                          </div>
                        </div>
                        <a
                          href=""https://hoclieu.vn/user-resource""
                          target=""_blank""
                          style=""
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding: 12px;
                            width: fit-content;
                            background: #244e8b;
                            border-radius: 16px;
                            font-style: normal;
                            font-weight: 500;
                            font-size: 14px;
                            color: #ffffff;
                            text-decoration: none;
                            margin: 12px auto;
                          ""
                          >KÍCH HOẠT NGAY</a
                        >
                        <div
                          style=""
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            color: #244e8b;
                          ""
                        >
                          <div>
                            Để dễ dàng khai thác các tính năng giúp luyện thi đạt hiệu quả tối
                            đa, <span style=""font-weight: 700"">Hoclieu</span> gửi bạn tham
                            khảo:
                          </div>
                          <div style=""padding-left: 5px"">
                            <div>
                              • Hướng dẫn luyện thi trên hệ thống
                              <a
                                href=""https://gioithieu.hoclieu.vn/docs-category/hoc-sinh/""
                                target=""_blank""
                                style=""font-weight: 700; font-style: italic; color: #244e8b;""
                                >tại đây</a
                              >.
                            </div>
                            <div>
                              • Video hướng dẫn chi tiết
                              <a
                                href=""https://www.youtube.com/playlist?list=PL_b2PIVBCqafBPuVtcrmpiWbDLcJm_36H""
                                target=""_blank""
                                style=""font-weight: 700; font-style: italic; color: #244e8b;""
                                >tại đây</a
                              >.
                            </div>
                          </div>

                          <div style=""margin: 12px 0"">
                            Nếu bạn cần thêm thông tin, vui lòng liên hệ
                            <a
                              href=""https://m.me/hoclieu.vn""
                              target=""_blank""
                              rel=""noopener noreferrer""
                              style=""font-weight: 700; color: #244e8b; font-style: italic;""
                              >kênh hỗ trợ</a
                            >
                            hoặc đường dây nóng:
                            <span style=""font-weight: 700"">(024) 3512.2222</span>
                          </div>
                          <div
                            style=""
                              margin-top: 45px;
                              font-weight: 600;
                              font-size: 14px;
                              line-height: 16px;
                              color: #244e8b;
                            ""
                          >
                            Chúc bạn luyện thi đạt kết quả cao nhất!
                          </div>
                          <div
                            style=""
                              margin-top: 5px;
                              font-weight: 700;
                              font-size: 20px;
                              color: #244e8b;
                            ""
                          >
                            Hoclieu
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class=""footer""
                      style=""
                        margin: auto;
                        background-color: #244e8b;
                        width: 595px;
                        height: 68px;
                        font-weight: 300;
                        font-size: 12px;
                        line-height: 20px;
                        color: #ffffff;
                        box-shadow: 0px 4px 5px 2px rgba(0, 0, 0, 0.25);
                      ""
                    >
                      <div
                        style=""
                          margin: 0 60px;
                          display: flex;
                          height: 68px;
                          justify-content: space-between;
                          align-items: center;
                          place-content: space-between;
                        ""
                      >
                        <div
                          class=""first""
                          style=""
                            display: flex;
                            height: fit-content;
                            width: 200px;
                            margin: auto 0;
                          ""
                        >
                          <span>Theo dõi:</span>

                          <a
                            href=""https://www.facebook.com/hoclieu.vn""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px; display: flex""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/facebook%20(1)%201%20(Traced).png""
                              width=""20""
                              height=""20""
                          /></a>
                          <a
                            href=""https://www.youtube.com/channel/UCIhr4c-891H2-dbjNLpvG-g""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px; display: flex""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/youtube-logotype%201%20(Traced).png""
                              width=""20""
                              height=""20""
                              alt=""""
                            />
                          </a>
                        </div>
                        <div
                          class=""mid""
                          style=""
                            display: flex;
                            height: fit-content;
                            width: 200px;
                            margin: auto 0;
                          ""
                        >
                          <span>Hỗ trợ:</span>
                          <a
                            href=""https://m.me/hoclieu.vn""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px; display: flex""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/messenger%201%20(Traced).png""
                              width=""20""
                              height=""20""
                              style=""margin: 0 5px""
                          /></a>
                        </div>
                        <div
                          class=""last""
                          style=""
                            display: flex;
                            height: fit-content;
                            margin: auto 0;
                          ""
                        >
                          <a
                            href=""https://hoclieu.vn/""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/Group%2010738.png""
                              width=""36""
                              height=""31""
                          /></a>
                        </div>
                      </div>
                    </div>
                  </div>";

            BackgroundJob.Enqueue(() => SendEmail(
                email,
                email,
                mailTitle,
                mailContent));
        }

        public void SendMailGiveGiftCard(string email, string name, int count)
        {
            var mailTitle = "[Hoclieu.vn] Tiếp sức mùa thi - Tặng ngay thẻ VIP";
            var mailContent = $@"
              <div style=""
                        font-family: Arial, Helvetica, sans-serif;
                        background-image: url(https://img.hoclieu.vn/hoclieu/shared-image/bg.png);
                        padding: 10px 0;"">
                    <div
                      class=""page""
                      style=""
                        margin: auto;
                        width: 595px;
                        height: 774px;
                        text-align: justify;
                        background-image: url(https://img.hoclieu.vn/hoclieu/shared-image/z3286698714732_f6944a67e54fc066788d7b28ef871e85.jpg);
                        box-shadow: 0px 4px 0px 2px rgba(0, 0, 0, 0.25);
                      ""
                    >
                      <div class=""content"" style=""margin: 0 60px"">
                        <div style=""text-align: center; padding: 40px 0"">
                          <img src=""https://img.hoclieu.vn/hoclieu/shared-image/logo-hoclieu.png"" width=""80"" height=""60"" />
                          <div
                            style=""
                              font-weight: 600;
                              font-size: 24px;
                              line-height: 32px;
                              color: #244e8b;
                              padding-top: 25px;
                            ""
                          >
                            CỔNG LUYỆN THI TỐT NGHIỆP THPT
                          </div>
                          <div
                            style=""
                              font-weight: 700;
                              font-size: 20px;
                              line-height: 28px;
                              color: #ff421d;
                              padding-top: 20px;
                            ""
                          >
                            Tiếp sức mùa thi - Tặng ngay thẻ VIP
                          </div>
                        </div>
                        <div
                          style=""
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            color: #244e8b;
                          ""
                        >
                          Xin chào
                          <span style=""font-weight: 700; font-style: italic""
                            >{name}</span
                          >,
                        </div>
                        <br>
                        <div
                          style=""
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            color: #244e8b;
                          ""
                        >
                          <div>
                            Nhằm tiếp sức cho các em học sinh 2k4 trong giai đoạn ôn thi gấp rút, Nhà xuất bản Giáo dục Việt Nam gửi tặng mỗi bạn <span style=""font-weight: 700"">{count} thẻ VIP</span> trên <a
                              href=""https://gioithieu.hoclieu.vn/cong-luyen-thi/""
                              target=""_blank""
                              style=""font-weight: 700; font-style: italic; color: #244E8B;""
                              >Cổng luyện thi tốt nghiệp THPT</a
                            >.
                          </div>
                          <br>
                          <div style=""font-weight: 700"">Điều kiện nhận thẻ:</div>
                          <div style=""padding-left: 5px"">
                            <div>
                              • Học sinh lớp 12 đang ôn thi THPT Quốc gia.
                            </div>
                            <div>
                              • Đã mua ít nhất 1 thẻ kích hoạt môn trên Cổng luyện thi tốt nghiệp THPT.
                            </div>
                          </div>

                        </div>
                        <div  style=""
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            color: #244e8b;
                          "">
                            Nhận thẻ ngay <a
                              href=""https://hoclieu.vn/user-resource""
                              target=""_blank""
                              style=""font-weight: 700; font-style: italic; color: #244E8B;""
                              >tại đây</a
                            > hoặc nhấn nút nhận thẻ bên dưới.
                          </div>
                        <a
                          href=""https://hoclieu.vn/user-resource""
                          target=""_blank""
                          style=""
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding: 12px;
                            width: fit-content;
                            background: #244e8b;
                            border-radius: 16px;
                            font-style: normal;
                            font-weight: 500;
                            font-size: 14px;
                            color: #ffffff;
                            text-decoration: none;
                            margin: 12px auto;
                          ""
                          >NHẬN THẺ NGAY</a
                        >
                          <div style=""margin-top: 12px 0; color: #244e8b;"">
                            Nếu bạn cần thêm thông tin, vui lòng liên hệ
                            <a
                              href=""https://m.me/hoclieu.vn""
                              target=""_blank""
                              rel=""noopener noreferrer""
                              style=""font-weight: 700; color: #244e8b; font-style: italic;""
                              >kênh hỗ trợ</a
                            >
                            hoặc đường dây nóng:
                            <span style=""font-weight: 700"">(024) 3512.2222</span>
                          </div>
                          <br>
                          <div
                            style=""
                              font-size: 14px;
                              line-height: 16px;
                              color: #244e8b;
                            ""
                          >
                            Chúc bạn luyện thi đạt kết quả cao nhất!
                          </div>
                          <br>
                          <div
                            style=""
                              font-size: 14px;
                              line-height: 16px;
                              font-style: italic;
                              color: #244e8b;
                            ""
                          >
                            Thân tặng,
                          </div>
                          <div
                            style=""
                              margin-top: 5px;
                              font-weight: 700;
                              font-size: 14px;
                              line-height: 16px;
                              color: #244e8b;
                            ""
                          >
                            Nhà xuất bản Giáo dục Việt Nam
                          </div>
                        </div>
                      </div>
                    <div
                      class=""footer""
                      style=""
                        margin: auto;
                        background-color: #244e8b;
                        width: 595px;
                        height: 68px;
                        font-weight: 300;
                        font-size: 12px;
                        line-height: 20px;
                        color: #ffffff;
                        box-shadow: 0px 4px 5px 2px rgba(0, 0, 0, 0.25);
                      ""
                    >
                      <div
                        style=""
                          margin: 0 60px;
                          display: flex;
                          height: 68px;
                          justify-content: space-between;
                          align-items: center;
                          place-content: space-between;
                        ""
                      >
                        <div
                          class=""first""
                          style=""
                            display: flex;
                            height: fit-content;
                            width: 200px;
                            margin: auto 0;
                          ""
                        >
                          <span>Theo dõi:</span>

                          <a
                            href=""https://www.facebook.com/hoclieu.vn""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px; display: flex""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/facebook%20(1)%201%20(Traced).png""
                              width=""20""
                              height=""20""
                          /></a>
                          <a
                            href=""https://www.youtube.com/channel/UCIhr4c-891H2-dbjNLpvG-g""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px; display: flex""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/youtube-logotype%201%20(Traced).png""
                              width=""20""
                              height=""20""
                              alt=""""
                            />
                          </a>
                        </div>
                        <div
                          class=""mid""
                          style=""
                            display: flex;
                            height: fit-content;
                            width: 200px;
                            margin: auto 0;
                          ""
                        >
                          <span>Hỗ trợ:</span>
                          <a
                            href=""https://m.me/hoclieu.vn""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px; display: flex""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/messenger%201%20(Traced).png""
                              width=""20""
                              height=""20""
                              style=""margin: 0 5px""
                          /></a>
                        </div>
                        <div
                          class=""last""
                          style=""
                            display: flex;
                            height: fit-content;
                            margin: auto 0;
                          ""
                        >
                          <a
                            href=""https://hoclieu.vn/""
                            target=""_blank""
                            rel=""noopener noreferrer""
                            style=""margin: 0 5px""
                          >
                            <img
                              src=""https://img.hoclieu.vn/hoclieu/shared-image/Group%2010738.png""
                              width=""36""
                              height=""31""
                          /></a>
                        </div>
                      </div>
                    </div>
                  </div>";

            BackgroundJob.Enqueue(() => SendEmail(
                email,
                email,
                mailTitle,
                mailContent));
        }

        /// <summary>
        /// Gửi email thông báo lỗi khi tạo đề thi
        /// </summary>
        public void SendMailExceptionCreateCheckpoint(CheckpointInfoDto checkpointInfo)
        {
            var mailTitle = "[Hoclieu.vn] Thông báo lỗi khi tạo đề thi";
            var mailContent = "";

            var numberOfQuestion = checkpointInfo.QuestionSets?.Sum(qs => qs.ListQuestion.Count) ?? 0;

            if (numberOfQuestion < checkpointInfo.NumberQuestion)
            {
                mailContent += $"Thiếu câu hỏi {numberOfQuestion}/{checkpointInfo.NumberQuestion}.<br/><br/>";
            }

            if (checkpointInfo.Exceptions.Count > 0)
            {
                mailContent += "<b>Lỗi khi tạo câu hỏi</b><br/>";
            }

            foreach (var exception in checkpointInfo.Exceptions)
            {
                mailContent += $@"<a
                        href=""https://hoclieu.vn/admin/skill/{exception.SkillId}/question?templateQuestionId={exception.NewTemplateQuestionId}&dataQuestionId={exception.NewDataQuestionId}""
                        target=""_blank""
                        rel=""noopener noreferrer"">
                        https://hoclieu.vn/admin/skill/{exception.SkillId}/question?templateQuestionId={exception.NewTemplateQuestionId}&dataQuestionId={exception.NewDataQuestionId}
                    </a>
                    <br/>";
                mailContent += $"<{exception.ExceptionMessage}<br/>";
            }

            if (mailContent == "")
            {
                return;
            }

            mailContent = $@"<a
                    href=""https://hoclieu.vn/checkpoints/{checkpointInfo.SkillId}""
                    target=""_blank""
                    rel=""noopener noreferrer"">
                    https://hoclieu.vn/checkpoints/{checkpointInfo.SkillId}
                </a>
                <br/><br/>" + mailContent;

            var email = "<EMAIL>";
            var emailCc1 = "<EMAIL>";
            var emailCc2 = "<EMAIL>";

            BackgroundJob.Enqueue(() => SendEmail(
                email,
                email,
                mailTitle,
                mailContent,
                emailCc1,
                emailCc2));
        }

    }
}
