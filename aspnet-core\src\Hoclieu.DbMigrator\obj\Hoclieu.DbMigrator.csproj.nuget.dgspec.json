{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.DbMigrator\\Hoclieu.DbMigrator.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj", "projectName": "Hoclieu.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Google.Cloud.TextToSpeech.V1": {"target": "Package", "version": "[3.0.0, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "System.Drawing.Common": {"target": "Package", "version": "[5.0.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.DbMigrator\\Hoclieu.DbMigrator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.DbMigrator\\Hoclieu.DbMigrator.csproj", "projectName": "Hoclieu.DbMigrator", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.DbMigrator\\Hoclieu.DbMigrator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.DbMigrator\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.EntityFrameworkCore\\Hoclieu.EntityFrameworkCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.EntityFrameworkCore\\Hoclieu.EntityFrameworkCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "c:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Domain\\Hoclieu.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Domain\\Hoclieu.Domain.csproj", "projectName": "Hoclieu.Domain", "projectPath": "c:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Domain\\Hoclieu.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[8.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.EntityFrameworkCore\\Hoclieu.EntityFrameworkCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.EntityFrameworkCore\\Hoclieu.EntityFrameworkCore.csproj", "projectName": "Hoclieu.EntityFrameworkCore", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.EntityFrameworkCore\\Hoclieu.EntityFrameworkCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.EntityFrameworkCore\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj"}, "c:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Domain\\Hoclieu.Domain.csproj": {"projectPath": "c:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Domain\\Hoclieu.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.6, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}