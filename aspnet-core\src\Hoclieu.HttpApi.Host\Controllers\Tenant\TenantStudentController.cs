namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Hoclieu.Core.Dtos.Tenant;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System;
using System.Linq;
using Hoclieu.Core.Dtos;
using Hoclieu.Services.User;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Users;
using Hoclieu.Domain.User;
using Hoclieu.Classrooms;
using System.Collections.Generic;
using Hoclieu.Core.Constant;
using System.Globalization;
using Hoclieu.Core.Enums.Tenant;
using Hoclieu.Core.Enums;

/// <summary>
///
/// </summary>
/// <remarks>
///
/// </remarks>
/// <param name="dbContext"></param>
/// <param name="tenancyService"></param>
/// <param name="tenancyUserManager"></param>
/// <param name="classroomStudentRepository"></param>
/// <param name="classroomTeacherRepository"></param>
/// <param name="tenantUserService"></param>
[Route("api/[controller]")]
[ApiController]
public class TenantStudentController(
    HoclieuDbContext dbContext,
    TenancyService tenancyService,
    TenancyUserManager tenancyUserManager,
    ClassroomStudentRepository classroomStudentRepository,
    ClassroomTeacherRepository classroomTeacherRepository,
    TenantUserService tenantUserService
        ) : ControllerBase
{
    private readonly HoclieuDbContext _dbContext = dbContext;
    private readonly TenancyService _tenancyService = tenancyService;
    private readonly TenancyUserManager _tenancyUserManager = tenancyUserManager;
    private readonly ClassroomStudentRepository _classroomStudentRepository = classroomStudentRepository;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository = classroomTeacherRepository;
    private readonly TenantUserService _tenantUserService = tenantUserService;


    /// <summary>
    /// Lấy danh sách học sinh trong tenant
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [Authorize(Role.TenantAdmin)]
    [HttpGet]
    public async Task<BaseResponse<GetListTenantStudentResponse>> GetList(
        [FromQuery] GetListTenantStudentRequest request)
    {
        long tenantClaimId = 0;
        if (HttpContext.Items["TenantId"] is string tenantCode)
        {
            tenantClaimId = await _tenancyService.GetCurrentTenantIdAsync(tenantCode);
        }

        // Build main query with joins to reduce database round trips
        var baseQuery = from tu in _dbContext.TenantUsers
                        join ur in _dbContext.UserRoles on tu.UserId equals ur.UserId
                        join r in _dbContext.Roles on ur.RoleId equals r.Id
                        where tu.TenantId == tenantClaimId
                              && r.Name == Role.Student
                              && ur.TenantId == tenantClaimId
                        select tu;

        // Apply status filter at database level if specified
        if (request.Status.HasValue)
        {
            baseQuery = from tu in baseQuery
                        join ts in _dbContext.TenantStudents on tu.Id equals ts.TenantUserId
                        where ts.Status == request.Status.Value
                        select tu;
        }

        // Apply classroom filter at database level if specified
        if (request.ClassroomId != Guid.Empty)
        {
            baseQuery = from tu in baseQuery
                        join cs in _dbContext.ClassroomStudents on tu.UserId equals cs.Student.UserId
                        where cs.ClassroomId == request.ClassroomId
                        select tu;
        }

        // Apply search filter at database level
        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            var searchTerm = request.Search.ToLower(CultureInfo.CurrentCulture);
            baseQuery = baseQuery.Where(tu =>
                (tu.FirstName + " " + tu.LastName).Contains(searchTerm, StringComparison.CurrentCultureIgnoreCase) ||
                (tu.Email != null && tu.Email.Contains(searchTerm, StringComparison.CurrentCultureIgnoreCase)));
        }

        // Get total count before pagination
        var totalItems = await baseQuery.CountAsync();

        // Apply pagination at database level and get only required fields
        var pagedResults = await baseQuery
            .AsNoTracking()
            .OrderBy(tu => tu.LastName)
            .ThenBy(tu => tu.FirstName)
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .Select(tu => new
            {
                tu.Id,
                tu.UserId,
                tu.LastName,
                tu.FirstName,
                tu.Email,
                tu.CitizenId,
                tu.UserName
            })
            .ToListAsync();

        var userIds = pagedResults.Select(x => x.UserId).ToList();
        var tenantUserIds = pagedResults.Select(x => x.Id).ToList();

        // Fetch additional data sequentially to avoid DbContext threading issues
        var classroomDict = await _dbContext.Classrooms
            .AsNoTracking()
            .Where(c => c.TenantId == tenantClaimId)
            .Select(c => new { c.Id, c.Name })
            .ToDictionaryAsync(c => c.Id, c => c.Name);

        var classroomStudents = await _dbContext.ClassroomStudents
            .AsNoTracking()
            .Where(cs => userIds.Contains(cs.Student.UserId))
            .Select(cs => new { cs.Student.UserId, cs.ClassroomId })
            .ToListAsync();

        var tenantStudentDict = await _dbContext.TenantStudents
            .AsNoTracking()
            .Where(ts => tenantUserIds.Contains(ts.TenantUserId))
            .Select(ts => new
            {
                ts.TenantUserId,
                ts.MOETStudentId,
                ts.Status
            })
            .ToDictionaryAsync(ts => ts.TenantUserId, ts => ts);

        // Build classroom dictionary for users
        var studentClassroomDict = classroomStudents
            .GroupBy(cs => cs.UserId)
            .ToDictionary(
                g => g.Key,
                g => g.Select(x => x.ClassroomId).Distinct().ToList()
            );

        // Build response
        var members = pagedResults.Select(user =>
        {
            var listClassroom = studentClassroomDict.TryGetValue(user.UserId, out var classroomIds)
                ? [.. classroomIds
                    .Where(classroomDict.ContainsKey)
                    .Select(id => new ClassroomFilterValue { Id = id, Name = classroomDict[id] })]
                : new List<ClassroomFilterValue>();

            tenantStudentDict.TryGetValue(user.Id, out var tenantStudent);

            return new TenantStudentInfo
            {
                Id = user.Id,
                UserId = user.UserId,
                LastName = user.LastName,
                FirstName = user.FirstName,
                Email = user.Email,
                CitizenId = user.CitizenId,
                MOETStudentId = tenantStudent?.MOETStudentId ?? "",
                Status = tenantStudent?.Status,
                ListClassroom = listClassroom,
                UserName = user.UserName
            };
        }).ToList();

        var listClassroomFilterValue = classroomDict
            .Select(kv => new ClassroomFilterValue { Id = kv.Key, Name = kv.Value })
            .ToList();

        var totalItemsNoFilter = await _dbContext.TenantUsers
            .AsNoTracking()
            .Join(_dbContext.UserRoles, tu => tu.UserId, ur => ur.UserId, (tu, ur) => new { tu, ur })
            .Join(_dbContext.Roles, x => x.ur.RoleId, r => r.Id, (x, r) => new { x.tu, x.ur, r })
            .Where(x => x.tu.TenantId == tenantClaimId && x.r.Name == Role.Student && x.ur.TenantId == tenantClaimId)
            .CountAsync();

        return new BaseResponse<GetListTenantStudentResponse>
        {
            Data = new GetListTenantStudentResponse
            {
                Members = members,
                TotalItemsNoFilter = totalItemsNoFilter,
                ListClassroomFilterValue = listClassroomFilterValue
            },
            StatusCode = StatusCodeConstant.Status200Ok,
            TotalItems = totalItems
        };
    }

    /// <summary>
    /// Lấy ra chi tiết hồ sơ học sinh trong tenant
    /// </summary>
    /// <returns></returns>
    [HttpGet("{tenantUserId}")]
    public async Task<ActionResult<BaseResponse<TenantStudentDto>>> GetById(long tenantUserId)
    {
        var tenantUser = await this._dbContext.TenantUsers
            .FirstOrDefaultAsync(tu => tu.Id == tenantUserId);

        if (tenantUser == null)
        {
            return NotFound(new BaseResponse<TenantStudentDto>
            {
                Message = "Không tìm thấy hồ sơ học sinh trong cơ sở giáo dục.",
                StatusCode = "404"
            });
        }

        var studentInfo = await this._dbContext.TenantStudents
            .FirstOrDefaultAsync(ts => ts.TenantUserId == tenantUser.Id);

        var addresses = await this._dbContext.AddressUsers
            .Where(a => a.Id == tenantUser.CurrentAddressId || a.Id == tenantUser.PermanentAddressId)
            .ToDictionaryAsync(a => a.Id, a => a);

        TenantUserAddress MapAddress(Guid? addressId)
        {
            if (addressId.HasValue && addresses.TryGetValue(addressId.Value, out var addr))
            {
                return new TenantUserAddress
                {
                    Id = addr.Id,
                    DetailAddress = addr.DetailAddress,
                    ProvinceId = addr.ProvinceId,
                    WardId = addr.WardId,
                    Nationality = addr.Nationality
                };
            }
            return null;
        }

        var result = new TenantStudentDto
        {
            Id = tenantUser.Id,
            UserId = tenantUser.UserId,
            FirstName = tenantUser.FirstName,
            LastName = tenantUser.LastName,
            Gender = tenantUser.Gender,
            Birthday = tenantUser.Birthday,
            Email = tenantUser.Email,
            Religion = tenantUser.Religion,
            CitizenId = tenantUser.CitizenId,
            Ethnicity = tenantUser.Ethnicity,
            CurrentAddress = MapAddress(tenantUser.CurrentAddressId),
            PermanentAddress = MapAddress(tenantUser.PermanentAddressId),
            Status = studentInfo?.Status,
            GradeId = studentInfo?.GradeId,
            MOETStudentId = studentInfo?.MOETStudentId,
            AdmissionMethod = studentInfo?.AdmissionMethod,
            PhoneNumber = tenantUser.PhoneNumber
        };


        return Ok(new BaseResponse<TenantStudentDto>
        {
            Data = result,
            StatusCode = "200",
            Message = "Success"
        });
    }

    /// <summary>
    /// Thêm hoặc cập nhật thông tin học sinh trong tenant.
    /// </summary>
    /// <param name="id">Id của học sinh trong tenant.</param>
    /// <param name="request">Thông tin cập nhật học sinh.</param>
    /// <returns>Kết quả thực hiện thêm hoặc cập nhật học sinh.</returns>
    [HttpPost("{id}/upsert")]
    [Authorize(Role.TenantAdmin)]
    public async Task<ActionResult<BaseResponse<object>>> Upsert(long id, [FromBody] UpdateStudentInfoRequest request)
    {
        var tenantStudentInfo = await this._dbContext.TenantStudents.FirstOrDefaultAsync(ts => ts.TenantUserId == id);

        if (tenantStudentInfo == null)
        {
            tenantStudentInfo = new TenantStudent
            {
                Id = Guid.NewGuid(),
                TenantUserId = id,
                Status = request.Status,
                MOETStudentId = request.MOETStudentId,
                AdmissionMethod = request.AdmissionMethod,
                GradeId = request.GradeId
            };
            this._dbContext.TenantStudents.Add(tenantStudentInfo);
        }
        else
        {
            tenantStudentInfo.Status = request.Status;
            tenantStudentInfo.MOETStudentId = request.MOETStudentId;
            tenantStudentInfo.AdmissionMethod = request.AdmissionMethod;
            tenantStudentInfo.GradeId = request.GradeId;
        }

        await this._dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<object>
        {
            Data = null,
            StatusCode = "200",
            Message = "Success"
        });
    }

    /// <summary>
    /// Lấy thông tin phụ huynh trong tenant
    /// </summary>
    /// <param name="tenantUserId">TenantUserId</param>
    /// <returns></returns>
    /// <exception cref="NullReferenceException"></exception>
    [HttpGet("{tenantUserId}/parent")]
    public async Task<ActionResult<BaseResponse<object>>> GetParent(long tenantUserId)
    {
        var tenantUser = await _dbContext.TenantUsers
            .Include(tu => tu.User)
            .FirstOrDefaultAsync(tu => tu.Id == tenantUserId);

        if (tenantUser?.User == null)
        {

            return NotFound(new BaseResponse<object>
            {
                Data = null,
                StatusCode = StatusCodeConstant.Status404NotFound,
                Message = "Không tìm thấy hồ sơ học sinh trong cơ sở giáo dức."
            });
        }


        var parentTenantUser = await (
            from student in _dbContext.Students
            where student.UserId == tenantUser.User.Id && student.SupervisorId != null

            join parent in _dbContext.Parents
                on student.SupervisorId equals parent.Id

            join parentTenant in _dbContext.TenantUsers
                on parent.UserId equals parentTenant.UserId

            where parentTenant.TenantId == tenantUser.TenantId

            select parentTenant
        ).FirstOrDefaultAsync();

        return Ok(new BaseResponse<object>
        {
            Data = parentTenantUser,
            StatusCode = "200",
            Message = "Success"
        });
    }

    /// <summary>
    /// Thêm học sinh từ excel
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="NullReferenceException"></exception>
    [HttpPost("add-range")]
    public async Task<ActionResult<BaseResponse<object>>> AddRange(ImportStudentInfoRequest request)
    {
        var gradeDict = _dbContext.Grades
            .ToDictionary(g => g.Level, g => g.Id);

        if (request.Students == null || request.Students.Count == 0)
        {
            return BadRequest(new BaseResponse<object>
            {
                Data = null,
                StatusCode = "400",
                Message = "Không có dữ liệu trong file excel."
            });
        }

        var sheetData = new List<object>();

        request.Students.ForEach(s =>
        {
            var citizenId = s.CitizenId;
            var firstName = s.FirstName;
            var lastName = s.LastName;

            var birthDay = !string.IsNullOrWhiteSpace(s.BirthDay) &&
               DateTime.TryParseExact(s.BirthDay, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var birthDate)
                ? birthDate
                : new DateTime(1970, 1, 1);

            var gender = MapGender(s.Gender);
            var status = MapTenantStudentStatus(s.Status);
            var admissionMethod = MapAdmissionMethod(s.AdmissionMethod);

            var rowData = new
            {
                FirstName = firstName,
                LastName = lastName,
                CitizenId = citizenId,
                s.MOETStudentId,
                BirthDay = birthDay,
                Status = status,
                s.Religion,
                s.Ethnicity,
                Gender = gender,
                s.GradeId,
                AdmissionMethod = admissionMethod,
                s.Email,
                s.PhoneNumber
            };

            sheetData.Add(rowData);
        });


        var tenantStudentDict = sheetData
        .Select((s, index) =>
        {
            dynamic item = s;
            var citizenId = (string)item.CitizenId;
            return new
            {
                CitizenId = citizenId,
                MOETStudentId = (string)item.MOETStudentId,
                item.Status,
                item.AdmissionMethod,
                item.GradeId,
                Index = index // Sử dụng index làm key backup
            };
        })
        .ToDictionary(s => !string.IsNullOrWhiteSpace(s.CitizenId) ? s.CitizenId : $"INDEX_{s.Index}", s => s);

        var tenantCode = (string)HttpContext.Items["TenantId"];
        var tenant = _dbContext.Tenants.FirstOrDefault(t => t.Code == tenantCode);

        var tenantUser = await _tenantUserService.AddMember(sheetData, tenant.Id, Role.Student);
        await _dbContext.TenantUsers.AddRangeAsync(tenantUser);

        await _dbContext.SaveChangesAsync();

        var listCitizenJustAdded = tenantUser.Select(tu => tu.CitizenId).ToList();

        // Match bằng CitizenId hoặc index cho những user không có CitizenId
        var tenantStudents = new List<dynamic>();
        for (int i = 0; i < tenantUser.Count; i++)
        {
            var user = tenantUser[i];
            var key = !string.IsNullOrWhiteSpace(user.CitizenId) ? user.CitizenId : $"INDEX_{i}";

            if (tenantStudentDict.TryGetValue(key, out var studentData))
            {
                tenantStudents.Add(new { Key = key, Value = studentData });
            }
        }

        var listTenantStudents = new List<TenantStudent>();

        foreach (var tenantStudent in tenantStudents)
        {
            var tenantStudentInfo = new TenantStudent
            {
                Id = Guid.NewGuid(),
                TenantUserId = tenantUser.First(tu => tu.CitizenId == tenantStudent.Key).Id,
                Status = tenantStudent.Value.Status,
                MOETStudentId = tenantStudent.Value.MOETStudentId,
                AdmissionMethod = tenantStudent.Value.AdmissionMethod,
                GradeId = tenantStudent.Value.GradeId
            };
            listTenantStudents.Add(tenantStudentInfo);
        }
        await _dbContext.TenantStudents.AddRangeAsync(listTenantStudents);

        var tenantUserIds = tenantUser.Select(tu => tu.UserId).ToList();
        if (tenantUserIds.Count > 0)
        {
            await _tenancyUserManager.AddUsersRoleToTenantAsync(tenantUserIds, Role.Student, tenant.Id);
        }
        await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<object>
        {
            Data = null,
            StatusCode = "200",
            Message = "Success"
        });
    }

    private static Gender? MapGender(string value) => value switch
    {
        "Nam" => Gender.Male,
        "Nữ" => Gender.Female,
        _ => Gender.Other
    };

    private static AdmissionMethod? MapAdmissionMethod(string value) => value switch
    {
        "Thi tuyển" => AdmissionMethod.Exam,
        "Xét tuyển" => AdmissionMethod.Review,
        _ => null
    };

    private static TenantStudentStatus? MapTenantStudentStatus(string value) => value switch
    {
        "Đang học" => TenantStudentStatus.Enrolling,
        "Bảo lưu" => TenantStudentStatus.Deferred,
        "Nghỉ học" => TenantStudentStatus.Dropped,
        _ => null
    };
}
