namespace Hoclieu.Services.User;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Core.Enums;
using Hoclieu.Domain.User;
using Hoclieu.Users;

public class TenantUserService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserService _userService;
    public TenantUserService(HoclieuDbContext dbContext, UserService userService)
    {
        _dbContext = dbContext;
        _userService = userService;

    }

    public async Task<List<TenantUser>> AddMember(List<object> userInfos, long tenantId, string role)
    {
        var result = new List<TenantUser>();
        var password = "12345678";

        if (userInfos == null || userInfos.Count == 0)
        {
            Console.WriteLine("[DEBUG] userInfos is null or empty");
            return result;
        }

        Console.WriteLine($"[DEBUG] AddMember called with {userInfos.Count} userInfos, tenantId: {tenantId}, role: {role}");

        var listCitizenIds = userInfos
            .Select(x => x.GetType().GetProperty("CitizenId")?.GetValue(x)?.ToString())
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Distinct() // Loại bỏ duplicate
            .ToList();

        // Debug logging
        Console.WriteLine($"[DEBUG] Total CitizenIds from sheetData: {listCitizenIds.Count}");
        Console.WriteLine($"[DEBUG] CitizenIds: {string.Join(", ", listCitizenIds)}");

        var alreadyAddToTenant = _dbContext.TenantUsers
            .Where(u => listCitizenIds.Contains(u.CitizenId) && u.TenantId == tenantId)
            .Select(u => u.CitizenId)
            .ToList();

        Console.WriteLine($"[DEBUG] Already in tenant: {alreadyAddToTenant.Count}");
        Console.WriteLine($"[DEBUG] Already in tenant CitizenIds: {string.Join(", ", alreadyAddToTenant)}");

        var notYetAddedCitizenIds = listCitizenIds
            .Where(id => !alreadyAddToTenant.Contains(id))
            .ToList();

        Console.WriteLine($"[DEBUG] Not yet added: {notYetAddedCitizenIds.Count}");
        Console.WriteLine($"[DEBUG] Not yet added CitizenIds: {string.Join(", ", notYetAddedCitizenIds)}");

        var alreadyHaveAccount = _dbContext.Users
            .Where(u => notYetAddedCitizenIds.Contains(u.CitizenId) && !string.IsNullOrWhiteSpace(u.CitizenId))
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .Where(u => !string.IsNullOrWhiteSpace(u.CitizenId)) // Double check
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        var notYetHaveAccount = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .ToList();

        var notYetHaveAccountInfo = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .Select(id =>
            {
                var userInfo = userInfos.FirstOrDefault(u =>
                    (string)u.GetType().GetProperty("CitizenId")?.GetValue(u) == id);

                if (userInfo == null)
                {
                    Console.WriteLine($"[DEBUG] UserInfo not found for CitizenId: {id}");
                }

                var firstName = (string)userInfo?.GetType().GetProperty("FirstName")?.GetValue(userInfo);
                var lastName = (string)userInfo?.GetType().GetProperty("LastName")?.GetValue(userInfo);
                var birthDay = (DateTime?)userInfo?.GetType().GetProperty("BirthDay")?.GetValue(userInfo) ?? default(DateTime);
                var email = (string)userInfo?.GetType().GetProperty("Email")?.GetValue(userInfo);
                var phoneNumber = (string)userInfo?.GetType().GetProperty("PhoneNumber")?.GetValue(userInfo);

                return new
                {
                    CitizenId = id,
                    Email = email,
                    PhoneNumber = phoneNumber,
                    FirstName = firstName,
                    LastName = lastName,
                    BirthDay = birthDay,
                };
            })
            .Where(x => !string.IsNullOrWhiteSpace(x.CitizenId)) // Lọc bỏ null CitizenId
            .ToDictionary(x => x.CitizenId, x => new { x.Email, x.PhoneNumber, x.FirstName, x.LastName, x.BirthDay });

        Console.WriteLine($"[DEBUG] notYetHaveAccount count: {notYetHaveAccount.Count}");
        Console.WriteLine($"[DEBUG] notYetHaveAccountInfo count: {notYetHaveAccountInfo.Count}");


        var userInfosNeedCreateAccount = userInfos
            .Where(u =>
            {
                var citizenId = (string)u.GetType().GetProperty("CitizenId")?.GetValue(u);
                return !string.IsNullOrWhiteSpace(citizenId) && notYetHaveAccount.Contains(citizenId);
            })
            .ToList();

        Console.WriteLine($"[DEBUG] userInfosNeedCreateAccount count: {userInfosNeedCreateAccount.Count}");

        var fullNameStudent = userInfosNeedCreateAccount
            .Select(u =>
            {
                var firstName = (string)u.GetType().GetProperty("FirstName")?.GetValue(u);
                var lastName = (string)u.GetType().GetProperty("LastName")?.GetValue(u);
                return $"{firstName} {lastName}".Trim();
            })
            .ToList();

        Console.WriteLine($"[DEBUG] fullNameStudent count: {fullNameStudent.Count}");

        var gradeId = _dbContext.Grades.FirstOrDefault(g => g.Level == 1).Id;

        var listAppUsersToCreate = _userService.GenerateUserFromFullName(fullNameStudent, password, gradeId);

        Console.WriteLine($"[DEBUG] listAppUsersToCreate count: {listAppUsersToCreate.Count}");
        Console.WriteLine($"[DEBUG] notYetHaveAccount count: {notYetHaveAccount.Count}");

        // Sử dụng approach an toàn hơn - iterate qua notYetHaveAccountInfo thay vì index
        var processedCount = 0;
        foreach (var kvp in notYetHaveAccountInfo)
        {
            if (processedCount >= listAppUsersToCreate.Count)
            {
                Console.WriteLine($"[DEBUG] Processed count {processedCount} exceeds listAppUsersToCreate bounds");
                break;
            }

            var citizenId = kvp.Key;
            var info = kvp.Value;

            if (string.IsNullOrWhiteSpace(citizenId))
            {
                Console.WriteLine($"[DEBUG] CitizenId is null or empty");
                continue;
            }

            Console.WriteLine($"[DEBUG] Processing CitizenId: {citizenId}");

            listAppUsersToCreate[processedCount].CitizenId = citizenId;
            listAppUsersToCreate[processedCount].Roles = [role];

            if (!string.IsNullOrWhiteSpace(info.Email))
            {
                listAppUsersToCreate[processedCount].Email = info.Email;
            }

            if (!string.IsNullOrWhiteSpace(info.PhoneNumber))
            {
                listAppUsersToCreate[processedCount].PhoneNumber = info.PhoneNumber;
            }

            if (!string.IsNullOrWhiteSpace(info.FirstName))
            {
                listAppUsersToCreate[processedCount].GivenName = info.FirstName;
            }

            if (!string.IsNullOrWhiteSpace(info.LastName))
            {
                listAppUsersToCreate[processedCount].FamilyName = info.LastName;
            }

            if (info.BirthDay != default(DateTime))
            {
                listAppUsersToCreate[processedCount].BirthDay = info.BirthDay;
            }

            processedCount++;
        }

        var appUsers = await _userService.CreateUsers(listAppUsersToCreate);
        var createdAccount = appUsers.Item1.Select(u => new { u.CitizenId, u.Id, u.UserName })
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        // Thêm existing users vào result trước
        var existingTenantUsers = _dbContext.TenantUsers
            .Where(tu => alreadyAddToTenant.Contains(tu.CitizenId) && tu.TenantId == tenantId)
            .ToList();

        Console.WriteLine($"[DEBUG] Adding {existingTenantUsers.Count} existing users to result");
        result.AddRange(existingTenantUsers);

        foreach (var userInfo in userInfos)
        {
            var type = userInfo.GetType();
            var citizenId = (string)type.GetProperty("CitizenId")?.GetValue(userInfo);
            if (string.IsNullOrWhiteSpace(citizenId))
            {
                continue;
            }

            // Nếu user đã tồn tại trong tenant, đã thêm vào result rồi
            if (alreadyAddToTenant.Contains(citizenId))
            {
                Console.WriteLine($"[DEBUG] User already exists, added to result: {citizenId}");
                continue;
            }

            // Chỉ xử lý user chưa có trong tenant
            if (!notYetAddedCitizenIds.Contains(citizenId))
            {
                continue;
            }

            var tenantUser = new TenantUser
            {
                FirstName = (string)type.GetProperty("FirstName")?.GetValue(userInfo),
                LastName = (string)type.GetProperty("LastName")?.GetValue(userInfo),
                Gender = (Gender)type.GetProperty("Gender")?.GetValue(userInfo),
                Birthday = (DateTime)type.GetProperty("BirthDay")?.GetValue(userInfo),
                PhoneNumber = (string)type.GetProperty("PhoneNumber")?.GetValue(userInfo),
                Email = (string)type.GetProperty("Email")?.GetValue(userInfo),
                Religion = (string)type.GetProperty("Religion")?.GetValue(userInfo),
                CitizenId = citizenId,
                Ethnicity = (string)type.GetProperty("Ethnicity")?.GetValue(userInfo),
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                TenantId = tenantId
            };

            if (alreadyHaveAccount.TryGetValue(citizenId, out var existing))
            {
                tenantUser.UserId = existing.Id;
                tenantUser.UserName = existing.UserName;
            }
            else if (createdAccount.TryGetValue(citizenId, out var newUser))
            {
                tenantUser.UserId = newUser.Id;
                tenantUser.UserName = newUser.UserName;
            }

            result.Add(tenantUser);
        }

        Console.WriteLine($"[DEBUG] Final result count: {result.Count}");
        Console.WriteLine($"[DEBUG] Result CitizenIds: {string.Join(", ", result.Select(r => r.CitizenId))}");

        // Nếu muốn lưu sau đó:
        // _dbContext.Users.AddRange(appUsers);
        // _dbContext.TenantUsers.AddRange(result);
        // await _dbContext.SaveChangesAsync();

        return result;
    }
}
