namespace Hoclieu.Services.User;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Core.Enums;
using Hoclieu.Domain.User;
using Hoclieu.Users;

public class TenantUserService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserService _userService;
    public TenantUserService(HoclieuDbContext dbContext, UserService userService)
    {
        _dbContext = dbContext;
        _userService = userService;

    }

    public async Task<List<TenantUser>> AddMember(List<object> userInfos, long tenantId, string role)
    {
        var result = new List<TenantUser>();
        var password = "12345678";

        if (userInfos == null || userInfos.Count == 0)
        {
            return result;
        }

        var listCitizenIds = userInfos
            .Select(x => x.GetType().GetProperty("CitizenId")?.GetValue(x)?.ToString())
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Distinct()
            .ToList();

        var alreadyAddToTenant = _dbContext.TenantUsers
            .Where(u => listCitizenIds.Contains(u.CitizenId) && u.TenantId == tenantId)
            .Select(u => u.CitizenId)
            .ToList();

        if (alreadyAddToTenant.Count > 0)
        {
            var duplicateNames = new List<string>();
            foreach (var duplicateCitizenId in alreadyAddToTenant)
            {
                var userInfo = userInfos.FirstOrDefault(u =>
                    (string)u.GetType().GetProperty("CitizenId")?.GetValue(u) == duplicateCitizenId);
                if (userInfo != null)
                {
                    var firstName = (string)userInfo.GetType().GetProperty("FirstName")?.GetValue(userInfo);
                    var lastName = (string)userInfo.GetType().GetProperty("LastName")?.GetValue(userInfo);
                    duplicateNames.Add($"{firstName} {lastName} (CitizenId: {duplicateCitizenId})");
                }
            }

            var errorMessage = duplicateNames.Count == 1
                ? $"Giáo viên {duplicateNames[0]} CitizenId trùng lặp."
                : $"Các giáo viên sau có CitizenId trùng lặp: {string.Join(", ", duplicateNames)}.";

            throw new ApplicationException(errorMessage);
        }

        var notYetAddedCitizenIds = listCitizenIds
            .Where(id => !alreadyAddToTenant.Contains(id))
            .ToList();

        var alreadyHaveAccount = _dbContext.Users
            .Where(u => notYetAddedCitizenIds.Contains(u.CitizenId) && !string.IsNullOrWhiteSpace(u.CitizenId))
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .Where(u => !string.IsNullOrWhiteSpace(u.CitizenId))
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        var notYetHaveAccount = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .ToList();

        var notYetHaveAccountInfo = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .Select(id =>
            {
                var userInfo = userInfos.FirstOrDefault(u =>
                    (string)u.GetType().GetProperty("CitizenId")?.GetValue(u) == id);

                var firstName = (string)userInfo?.GetType().GetProperty("FirstName")?.GetValue(userInfo);
                var lastName = (string)userInfo?.GetType().GetProperty("LastName")?.GetValue(userInfo);
                var birthDay = (DateTime?)userInfo?.GetType().GetProperty("BirthDay")?.GetValue(userInfo) ?? (new DateTime(1970, 1, 1));
                var email = (string)userInfo?.GetType().GetProperty("Email")?.GetValue(userInfo);
                var phoneNumber = (string)userInfo?.GetType().GetProperty("PhoneNumber")?.GetValue(userInfo);

                return new
                {
                    CitizenId = id,
                    Email = email,
                    PhoneNumber = phoneNumber,
                    FirstName = firstName,
                    LastName = lastName,
                    BirthDay = birthDay,
                };
            })
            .Where(x => !string.IsNullOrWhiteSpace(x.CitizenId)) // Lọc bỏ null CitizenId
            .ToDictionary(x => x.CitizenId, x => new { x.Email, x.PhoneNumber, x.FirstName, x.LastName, x.BirthDay });


        var userInfosNeedCreateAccount = userInfos
            .Where(u =>
            {
                var citizenId = (string)u.GetType().GetProperty("CitizenId")?.GetValue(u);
                return !string.IsNullOrWhiteSpace(citizenId) && notYetHaveAccount.Contains(citizenId);
            })
            .ToList();

        var fullNameStudent = userInfosNeedCreateAccount
            .Select(u =>
            {
                var firstName = (string)u.GetType().GetProperty("FirstName")?.GetValue(u);
                var lastName = (string)u.GetType().GetProperty("LastName")?.GetValue(u);
                return $"{firstName} {lastName}".Trim();
            })
            .ToList();

        var gradeId = _dbContext.Grades.FirstOrDefault(g => g.Level == 1).Id;

        var listAppUsersToCreate = _userService.GenerateUserFromFullName(fullNameStudent, password, gradeId);

        var processedCount = 0;
        foreach (var kvp in notYetHaveAccountInfo)
        {
            if (processedCount >= listAppUsersToCreate.Count)
            {
                break;
            }

            var citizenId = kvp.Key;
            var info = kvp.Value;

            if (string.IsNullOrWhiteSpace(citizenId))
            {
                continue;
            }

            listAppUsersToCreate[processedCount].CitizenId = citizenId;
            listAppUsersToCreate[processedCount].Roles = [role];

            if (!string.IsNullOrWhiteSpace(info.Email))
            {
                listAppUsersToCreate[processedCount].Email = info.Email;
            }

            if (!string.IsNullOrWhiteSpace(info.PhoneNumber))
            {
                listAppUsersToCreate[processedCount].PhoneNumber = info.PhoneNumber;
            }

            if (!string.IsNullOrWhiteSpace(info.FirstName))
            {
                listAppUsersToCreate[processedCount].GivenName = info.FirstName;
            }

            if (!string.IsNullOrWhiteSpace(info.LastName))
            {
                listAppUsersToCreate[processedCount].FamilyName = info.LastName;
            }

            if (info.BirthDay != new DateTime(1970, 1, 1))
            {
                listAppUsersToCreate[processedCount].BirthDay = info.BirthDay;
            }

            processedCount++;
        }

        var appUsers = await _userService.CreateUsers(listAppUsersToCreate);

        var validCreatedUsers = appUsers.Item1
            .Where(u => u.Id != Guid.Empty && !string.IsNullOrWhiteSpace(u.CitizenId))
            .ToList();

        var createdAccount = validCreatedUsers
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .Where(u => !string.IsNullOrWhiteSpace(u.CitizenId))
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        var existingTenantUsers = _dbContext.TenantUsers
            .Where(tu => alreadyAddToTenant.Contains(tu.CitizenId) && tu.TenantId == tenantId)
            .AsNoTracking()
            .ToList();

        foreach (var existing in existingTenantUsers)
        {
            // Tạo copy với Id = -1 để đánh dấu là existing user (không insert)
            var existingCopy = new TenantUser
            {
                Id = -1, // Flag để đánh dấu existing
                UserId = existing.UserId,
                TenantId = existing.TenantId,
                CitizenId = existing.CitizenId,
                FirstName = existing.FirstName,
                LastName = existing.LastName,
                Gender = existing.Gender,
                Birthday = existing.Birthday,
                PhoneNumber = existing.PhoneNumber,
                Email = existing.Email,
                Religion = existing.Religion,
                Ethnicity = existing.Ethnicity,
                UserName = existing.UserName,
                CreatedDate = existing.CreatedDate,
                ModifiedDate = existing.ModifiedDate
            };
            result.Add(existingCopy);
        }

        foreach (var userInfo in userInfos)
        {
            var type = userInfo.GetType();
            var citizenId = (string)type.GetProperty("CitizenId")?.GetValue(userInfo);
            if (string.IsNullOrWhiteSpace(citizenId))
            {
                continue;
            }

            // Nếu user đã tồn tại trong tenant, đã thêm vào result rồi
            if (alreadyAddToTenant.Contains(citizenId))
            {
                continue;
            }

            // Chỉ xử lý user chưa có trong tenant
            if (!notYetAddedCitizenIds.Contains(citizenId))
            {
                continue;
            }

            var tenantUser = new TenantUser
            {
                FirstName = (string)type.GetProperty("FirstName")?.GetValue(userInfo),
                LastName = (string)type.GetProperty("LastName")?.GetValue(userInfo),
                Gender = (Gender)type.GetProperty("Gender")?.GetValue(userInfo),
                Birthday = (DateTime)type.GetProperty("BirthDay")?.GetValue(userInfo),
                PhoneNumber = (string)type.GetProperty("PhoneNumber")?.GetValue(userInfo),
                Email = (string)type.GetProperty("Email")?.GetValue(userInfo),
                Religion = (string)type.GetProperty("Religion")?.GetValue(userInfo),
                CitizenId = citizenId,
                Ethnicity = (string)type.GetProperty("Ethnicity")?.GetValue(userInfo),
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                TenantId = tenantId
            };

            if (alreadyHaveAccount.TryGetValue(citizenId, out var existing))
            {
                tenantUser.UserId = existing.Id;
                tenantUser.UserName = existing.UserName;
            }
            else if (createdAccount.TryGetValue(citizenId, out var newUser))
            {
                tenantUser.UserId = newUser.Id;
                tenantUser.UserName = newUser.UserName;
            }
            else
            {
                // Không thêm user này vào result vì không có UserId hợp lệ
                continue;
            }

            result.Add(tenantUser);
        }

        return result;
    }
}
