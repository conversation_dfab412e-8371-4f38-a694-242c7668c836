namespace Hoclieu.Services.User;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Core.Enums;
using Hoclieu.Domain.User;
using Hoclieu.Users;

public class TenantUserService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserService _userService;
    public TenantUserService(HoclieuDbContext dbContext, UserService userService)
    {
        _dbContext = dbContext;
        _userService = userService;

    }

    public async Task<(List<TenantUser> users, List<string> errors)> AddMember(List<object> userInfos, long tenantId, string role)
    {
        var result = new List<TenantUser>();
        var errors = new List<string>();
        var password = "12345678";

        if (userInfos == null || userInfos.Count == 0)
        {
            Console.WriteLine("[DEBUG] userInfos is null or empty");
            return (result, errors);
        }

        Console.WriteLine($"[DEBUG] AddMember called with {userInfos.Count} userInfos, tenantId: {tenantId}, role: {role}");

        // Debug: Log first userInfo
        if (userInfos.Count > 0)
        {
            var firstUser = userInfos[0];
            var citizenId = firstUser.GetType().GetProperty("CitizenId")?.GetValue(firstUser)?.ToString();
            var firstName = firstUser.GetType().GetProperty("FirstName")?.GetValue(firstUser)?.ToString();
            var lastName = firstUser.GetType().GetProperty("LastName")?.GetValue(firstUser)?.ToString();
            Console.WriteLine($"[DEBUG] First user: CitizenId={citizenId}, FirstName={firstName}, LastName={lastName}");
        }

        var listCitizenIds = userInfos
            .Select(x => x.GetType().GetProperty("CitizenId")?.GetValue(x)?.ToString())
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Distinct() // Loại bỏ duplicate
            .ToList();

        // Debug logging
        Console.WriteLine($"[DEBUG] Total CitizenIds from sheetData: {listCitizenIds.Count}");
        Console.WriteLine($"[DEBUG] CitizenIds: {string.Join(", ", listCitizenIds)}");

        var alreadyAddToTenant = _dbContext.TenantUsers
            .Where(u => listCitizenIds.Contains(u.CitizenId) && u.TenantId == tenantId)
            .Select(u => u.CitizenId)
            .ToList();

        Console.WriteLine($"[DEBUG] Already in tenant: {alreadyAddToTenant.Count}");
        Console.WriteLine($"[DEBUG] Already in tenant CitizenIds: {string.Join(", ", alreadyAddToTenant)}");

        // Thêm error messages cho CitizenId đã tồn tại
        foreach (var duplicateCitizenId in alreadyAddToTenant)
        {
            var userInfo = userInfos.FirstOrDefault(u =>
                (string)u.GetType().GetProperty("CitizenId")?.GetValue(u) == duplicateCitizenId);
            if (userInfo != null)
            {
                var firstName = (string)userInfo.GetType().GetProperty("FirstName")?.GetValue(userInfo);
                var lastName = (string)userInfo.GetType().GetProperty("LastName")?.GetValue(userInfo);
                errors.Add($"Giáo viên {firstName} {lastName} (CCCD: {duplicateCitizenId}) đã tồn tại trong trường này.");
            }
        }

        var notYetAddedCitizenIds = listCitizenIds
            .Where(id => !alreadyAddToTenant.Contains(id))
            .ToList();

        Console.WriteLine($"[DEBUG] Not yet added: {notYetAddedCitizenIds.Count}");
        Console.WriteLine($"[DEBUG] Not yet added CitizenIds: {string.Join(", ", notYetAddedCitizenIds)}");

        var alreadyHaveAccount = _dbContext.Users
            .Where(u => notYetAddedCitizenIds.Contains(u.CitizenId) && !string.IsNullOrWhiteSpace(u.CitizenId))
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .Where(u => !string.IsNullOrWhiteSpace(u.CitizenId)) // Double check
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        var notYetHaveAccount = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .ToList();

        var notYetHaveAccountInfo = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .Select(id =>
            {
                var userInfo = userInfos.FirstOrDefault(u =>
                    (string)u.GetType().GetProperty("CitizenId")?.GetValue(u) == id);

                if (userInfo == null)
                {
                    Console.WriteLine($"[DEBUG] UserInfo not found for CitizenId: {id}");
                }

                var firstName = (string)userInfo?.GetType().GetProperty("FirstName")?.GetValue(userInfo);
                var lastName = (string)userInfo?.GetType().GetProperty("LastName")?.GetValue(userInfo);
                var birthDay = (DateTime?)userInfo?.GetType().GetProperty("BirthDay")?.GetValue(userInfo) ?? default(DateTime);
                var email = (string)userInfo?.GetType().GetProperty("Email")?.GetValue(userInfo);
                var phoneNumber = (string)userInfo?.GetType().GetProperty("PhoneNumber")?.GetValue(userInfo);

                return new
                {
                    CitizenId = id,
                    Email = email,
                    PhoneNumber = phoneNumber,
                    FirstName = firstName,
                    LastName = lastName,
                    BirthDay = birthDay,
                };
            })
            .Where(x => !string.IsNullOrWhiteSpace(x.CitizenId)) // Lọc bỏ null CitizenId
            .ToDictionary(x => x.CitizenId, x => new { x.Email, x.PhoneNumber, x.FirstName, x.LastName, x.BirthDay });

        Console.WriteLine($"[DEBUG] notYetHaveAccount count: {notYetHaveAccount.Count}");
        Console.WriteLine($"[DEBUG] notYetHaveAccountInfo count: {notYetHaveAccountInfo.Count}");


        var userInfosNeedCreateAccount = userInfos
            .Where(u =>
            {
                var citizenId = (string)u.GetType().GetProperty("CitizenId")?.GetValue(u);
                return !string.IsNullOrWhiteSpace(citizenId) && notYetHaveAccount.Contains(citizenId);
            })
            .ToList();

        Console.WriteLine($"[DEBUG] userInfosNeedCreateAccount count: {userInfosNeedCreateAccount.Count}");

        var fullNameStudent = userInfosNeedCreateAccount
            .Select(u =>
            {
                var firstName = (string)u.GetType().GetProperty("FirstName")?.GetValue(u);
                var lastName = (string)u.GetType().GetProperty("LastName")?.GetValue(u);
                return $"{firstName} {lastName}".Trim();
            })
            .ToList();

        Console.WriteLine($"[DEBUG] fullNameStudent count: {fullNameStudent.Count}");

        var gradeId = _dbContext.Grades.FirstOrDefault(g => g.Level == 1).Id;

        var listAppUsersToCreate = _userService.GenerateUserFromFullName(fullNameStudent, password, gradeId);

        Console.WriteLine($"[DEBUG] listAppUsersToCreate count: {listAppUsersToCreate.Count}");
        Console.WriteLine($"[DEBUG] notYetHaveAccount count: {notYetHaveAccount.Count}");

        // Sử dụng approach an toàn hơn - iterate qua notYetHaveAccountInfo thay vì index
        var processedCount = 0;
        foreach (var kvp in notYetHaveAccountInfo)
        {
            if (processedCount >= listAppUsersToCreate.Count)
            {
                Console.WriteLine($"[DEBUG] Processed count {processedCount} exceeds listAppUsersToCreate bounds");
                break;
            }

            var citizenId = kvp.Key;
            var info = kvp.Value;

            if (string.IsNullOrWhiteSpace(citizenId))
            {
                Console.WriteLine($"[DEBUG] CitizenId is null or empty");
                continue;
            }

            Console.WriteLine($"[DEBUG] Processing CitizenId: {citizenId}");
            Console.WriteLine($"[DEBUG] Setting CitizenId for listAppUsersToCreate[{processedCount}]");

            listAppUsersToCreate[processedCount].CitizenId = citizenId;
            listAppUsersToCreate[processedCount].Roles = [role];

            Console.WriteLine($"[DEBUG] After setting: listAppUsersToCreate[{processedCount}].CitizenId = {listAppUsersToCreate[processedCount].CitizenId}");

            if (!string.IsNullOrWhiteSpace(info.Email))
            {
                listAppUsersToCreate[processedCount].Email = info.Email;
            }

            if (!string.IsNullOrWhiteSpace(info.PhoneNumber))
            {
                listAppUsersToCreate[processedCount].PhoneNumber = info.PhoneNumber;
            }

            if (!string.IsNullOrWhiteSpace(info.FirstName))
            {
                listAppUsersToCreate[processedCount].GivenName = info.FirstName;
            }

            if (!string.IsNullOrWhiteSpace(info.LastName))
            {
                listAppUsersToCreate[processedCount].FamilyName = info.LastName;
            }

            if (info.BirthDay != default(DateTime))
            {
                listAppUsersToCreate[processedCount].BirthDay = info.BirthDay;
            }

            processedCount++;
        }

        Console.WriteLine($"[DEBUG] About to call CreateUsers with {listAppUsersToCreate.Count} requests");
        for (int i = 0; i < listAppUsersToCreate.Count; i++)
        {
            var req = listAppUsersToCreate[i];
            Console.WriteLine($"[DEBUG] Request[{i}]: CitizenId={req.CitizenId}, UserName={req.UserName}, GivenName={req.GivenName}, FamilyName={req.FamilyName}");
        }

        var appUsers = await _userService.CreateUsers(listAppUsersToCreate);

        Console.WriteLine($"[DEBUG] CreateUsers returned {appUsers.Item1.Count} users");
        Console.WriteLine($"[DEBUG] CreateUsers messages: {string.Join(", ", appUsers.Item2)}");

        // Filter ra những users tạo thành công và có CitizenId hợp lệ
        var validCreatedUsers = appUsers.Item1
            .Where(u => u.Id != Guid.Empty && !string.IsNullOrWhiteSpace(u.CitizenId))
            .ToList();

        var failedUsers = appUsers.Item1
            .Where(u => u.Id == Guid.Empty || string.IsNullOrWhiteSpace(u.CitizenId))
            .ToList();

        Console.WriteLine($"[DEBUG] Valid created users: {validCreatedUsers.Count}");
        Console.WriteLine($"[DEBUG] Failed users: {failedUsers.Count}");

        foreach (var failed in failedUsers)
        {
            Console.WriteLine($"[DEBUG] Failed user: CitizenId={failed.CitizenId}, UserName={failed.UserName}, Id={failed.Id}");
        }

        var createdAccount = validCreatedUsers
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .Where(u => !string.IsNullOrWhiteSpace(u.CitizenId)) // Double check
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        // Lấy existing users và thêm vào result (nhưng đánh dấu là existing)
        var existingTenantUsers = _dbContext.TenantUsers
            .Where(tu => alreadyAddToTenant.Contains(tu.CitizenId) && tu.TenantId == tenantId)
            .AsNoTracking() // Quan trọng: không track để tránh EF issues
            .ToList();

        Console.WriteLine($"[DEBUG] Found {existingTenantUsers.Count} existing users");

        // Thêm existing users vào result với flag để phân biệt
        foreach (var existing in existingTenantUsers)
        {
            Console.WriteLine($"[DEBUG] Adding existing user to result: CitizenId={existing.CitizenId}");
            // Tạo copy với Id = -1 để đánh dấu là existing user (không insert)
            var existingCopy = new TenantUser
            {
                Id = -1, // Flag để đánh dấu existing
                UserId = existing.UserId,
                TenantId = existing.TenantId,
                CitizenId = existing.CitizenId,
                FirstName = existing.FirstName,
                LastName = existing.LastName,
                Gender = existing.Gender,
                Birthday = existing.Birthday,
                PhoneNumber = existing.PhoneNumber,
                Email = existing.Email,
                Religion = existing.Religion,
                Ethnicity = existing.Ethnicity,
                UserName = existing.UserName,
                CreatedDate = existing.CreatedDate,
                ModifiedDate = existing.ModifiedDate
            };
            result.Add(existingCopy);
        }

        foreach (var userInfo in userInfos)
        {
            var type = userInfo.GetType();
            var citizenId = (string)type.GetProperty("CitizenId")?.GetValue(userInfo);
            if (string.IsNullOrWhiteSpace(citizenId))
            {
                continue;
            }

            // Nếu user đã tồn tại trong tenant, đã thêm vào result rồi
            if (alreadyAddToTenant.Contains(citizenId))
            {
                Console.WriteLine($"[DEBUG] User already exists, added to result: {citizenId}");
                continue;
            }

            // Chỉ xử lý user chưa có trong tenant
            if (!notYetAddedCitizenIds.Contains(citizenId))
            {
                continue;
            }

            var tenantUser = new TenantUser
            {
                FirstName = (string)type.GetProperty("FirstName")?.GetValue(userInfo),
                LastName = (string)type.GetProperty("LastName")?.GetValue(userInfo),
                Gender = (Gender)type.GetProperty("Gender")?.GetValue(userInfo),
                Birthday = (DateTime)type.GetProperty("BirthDay")?.GetValue(userInfo),
                PhoneNumber = (string)type.GetProperty("PhoneNumber")?.GetValue(userInfo),
                Email = (string)type.GetProperty("Email")?.GetValue(userInfo),
                Religion = (string)type.GetProperty("Religion")?.GetValue(userInfo),
                CitizenId = citizenId,
                Ethnicity = (string)type.GetProperty("Ethnicity")?.GetValue(userInfo),
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                TenantId = tenantId
            };

            if (alreadyHaveAccount.TryGetValue(citizenId, out var existing))
            {
                tenantUser.UserId = existing.Id;
                tenantUser.UserName = existing.UserName;
                Console.WriteLine($"[DEBUG] Set UserId from existing account: CitizenId={citizenId}, UserId={existing.Id}");
            }
            else if (createdAccount.TryGetValue(citizenId, out var newUser))
            {
                tenantUser.UserId = newUser.Id;
                tenantUser.UserName = newUser.UserName;
                Console.WriteLine($"[DEBUG] Set UserId from new account: CitizenId={citizenId}, UserId={newUser.Id}");
            }
            else
            {
                Console.WriteLine($"[DEBUG] WARNING: No UserId found for CitizenId={citizenId}, UserId will be empty");
                // Không thêm user này vào result vì không có UserId hợp lệ
                continue;
            }

            result.Add(tenantUser);
        }

        Console.WriteLine($"[DEBUG] Final result count: {result.Count}");
        Console.WriteLine($"[DEBUG] Result CitizenIds: {string.Join(", ", result.Select(r => r.CitizenId))}");
        Console.WriteLine($"[DEBUG] Total errors: {errors.Count}");

        // Nếu muốn lưu sau đó:
        // _dbContext.Users.AddRange(appUsers);
        // _dbContext.TenantUsers.AddRange(result);
        // await _dbContext.SaveChangesAsync();

        return (result, errors);
    }
}
