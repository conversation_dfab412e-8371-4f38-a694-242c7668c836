namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using EntityFrameworkCore;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Users;
using Hoclieu.Core.Dtos.Tenant;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using Hoclieu.Core.Dtos;
using Hoclieu.Domain.Tenant;
using Microsoft.AspNetCore.Identity;
using Hoclieu.Domain.User;
using Hoclieu.Services.User;
using Hoclieu.Classrooms;
using Hoclieu.Core.Helpers;
using Hoclieu.Core.Constant;
using Hoclieu.Core.Enums;
using Hoclieu.GoogleServices;
using System.Globalization;
using Hoclieu.Core.Enums.Tenant;
using Hangfire.Common;
using Google.Apis.Sheets.v4.Data;

/// <summary>
///
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class TenantTeacherController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly TenancyService _tenancyService;
    private readonly TenancyUserManager _tenancyUserManager;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository;
    TenantUserService _tenantUserService;

    /// <summary>
    ///
    /// </summary>
    /// <param name="dbContext"></param>
    /// <param name="userManager"></param>
    /// <param name="roleManager"></param>
    public TenantTeacherController
    (
        HoclieuDbContext dbContext,
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        TenancyService tenancyService,
        TenancyUserManager tenancyUserManager,
        ClassroomTeacherRepository classroomTeacherRepository,
        TenantUserService tenantUserService
        )
    {
        _dbContext = dbContext;
        _userManager = userManager;
        _roleManager = roleManager;
        _tenancyService = tenancyService;
        _tenancyUserManager = tenancyUserManager;
        _classroomTeacherRepository = classroomTeacherRepository;
        _tenantUserService = tenantUserService;
    }

    [Authorize(Role.TenantAdmin)]
    [HttpGet]
    public async Task<BaseResponse<GetListTenantTeacherResponse>> GetTeachersInTenant([FromQuery] GetListTenantTeacherRequest request)
    {
        long tenantClaimId = 0;
        if (HttpContext.Items["TenantId"] is string tenantCode)
        {
            tenantClaimId = await _tenancyService.GetCurrentTenantIdAsync(tenantCode);
        }

        // Build main query with joins to reduce database round trips
        var baseQuery = from tu in _dbContext.TenantUsers
                        join ur in _dbContext.UserRoles on tu.UserId equals ur.UserId
                        join r in _dbContext.Roles on ur.RoleId equals r.Id
                        where tu.TenantId == tenantClaimId
                              && r.Name == Role.Teacher
                              && ur.TenantId == tenantClaimId
                        select tu;

        // Apply status filter at database level if specified
        if (request.Status.HasValue)
        {
            baseQuery = from tu in baseQuery
                        join tt in _dbContext.TenantTeachers on tu.Id equals tt.TenantUserId
                        where tt.TeacherStatus == request.Status.Value
                        select tu;
        }

        // Apply classroom filter at database level if specified
        if (request.ClassroomId != Guid.Empty)
        {
            baseQuery = from tu in baseQuery
                        join t in _dbContext.Teachers on tu.UserId equals t.UserId
                        join ct in _dbContext.ClassroomTeachers on t.Id equals ct.TeacherId
                        where ct.ClassroomId == request.ClassroomId
                        select tu;
        }

        // Apply search filter at database level
        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            var searchTerm = request.Search.ToLower();
            baseQuery = baseQuery.Where(tu =>
                (tu.FirstName + " " + tu.LastName).ToLower().Contains(searchTerm) ||
                (tu.Email != null && tu.Email.ToLower().Contains(searchTerm)));
        }

        // Get total count before pagination
        var totalItems = await baseQuery.CountAsync();

        // Apply pagination at database level and get only required fields
        var pagedResults = await baseQuery
            .AsNoTracking()
            .OrderBy(tu => tu.LastName)
            .ThenBy(tu => tu.FirstName)
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .Select(tu => new
            {
                tu.Id,
                tu.UserId,
                tu.LastName,
                tu.FirstName,
                tu.Email,
                tu.UserName
            })
            .ToListAsync();

        var userIds = pagedResults.Select(x => x.UserId).ToList();
        var tenantUserIds = pagedResults.Select(x => x.Id).ToList();

        // Fetch additional data sequentially to avoid DbContext threading issues
        var classroomDict = await _dbContext.Classrooms
            .AsNoTracking()
            .Where(c => c.TenantId == tenantClaimId)
            .Select(c => new { c.Id, c.Name })
            .ToDictionaryAsync(c => c.Id, c => c.Name);

        var classroomTeachers = await _dbContext.ClassroomTeachers
            .AsNoTracking()
            .Join(_dbContext.Teachers, ct => ct.TeacherId, t => t.Id, (ct, t) => new { ct, t })
            .Where(x => userIds.Contains(x.t.UserId) && x.ct.Role == ClassroomRole.Owner)
            .Select(x => new { x.t.UserId, x.ct.ClassroomId })
            .ToListAsync();

        var tenantTeacherDict = await _dbContext.TenantTeachers
            .AsNoTracking()
            .Where(tt => tenantUserIds.Contains(tt.TenantUserId))
            .Select(tt => new
            {
                tt.TenantUserId,
                tt.TeacherCode,
                tt.TeacherStatus
            })
            .ToDictionaryAsync(tt => tt.TenantUserId, tt => tt);

        // Build classroom dictionary for users
        var teacherClassroomDict = classroomTeachers
            .GroupBy(ct => ct.UserId)
            .ToDictionary(
                g => g.Key,
                g => g.Select(x => x.ClassroomId).Distinct().ToList()
            );

        // Build response
        var members = pagedResults.Select(user =>
        {
            var listClassroom = teacherClassroomDict.TryGetValue(user.UserId, out var classroomIds)
                ? classroomIds
                    .Where(classroomDict.ContainsKey)
                    .Select(id => new ClassroomFilterValue { Id = id, Name = classroomDict[id] })
                    .ToList()
                : new List<ClassroomFilterValue>();

            tenantTeacherDict.TryGetValue(user.Id, out var tenantTeacher);

            return new TenantTeacherInfo
            {
                Id = user.Id,
                UserId = user.UserId,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                TeacherCode = tenantTeacher?.TeacherCode ?? "",
                Status = tenantTeacher?.TeacherStatus,
                ListClassroom = listClassroom,
                UserName = user.UserName
            };
        }).ToList();

        var listClassroomFilterValue = classroomDict
            .Select(kv => new ClassroomFilterValue { Id = kv.Key, Name = kv.Value })
            .ToList();

        var totalItemsNoFilter = await _dbContext.TenantUsers
            .AsNoTracking()
            .Join(_dbContext.UserRoles, tu => tu.UserId, ur => ur.UserId, (tu, ur) => new { tu, ur })
            .Join(_dbContext.Roles, x => x.ur.RoleId, r => r.Id, (x, r) => new { x.tu, x.ur, r })
            .Where(x => x.tu.TenantId == tenantClaimId && x.r.Name == Role.Teacher && x.ur.TenantId == tenantClaimId)
            .CountAsync();

        return new BaseResponse<GetListTenantTeacherResponse>
        {
            Data = new GetListTenantTeacherResponse
            {
                Members = members,
                TotalItemsNoFilter = totalItemsNoFilter,
                ListClassroomFilterValue = listClassroomFilterValue
            },
            StatusCode = StatusCodeConstant.Status200Ok,
            TotalItems = totalItems
        };
    }


    /// <summary>
    /// Lấy ra chi tiết hồ sơ học sinh trong tenant
    /// </summary>
    /// <returns></returns>
    [HttpGet("{tenantUserId}")]
    public async Task<ActionResult<BaseResponse<TenantTeacherDto>>> GetById(long tenantUserId)
    {
        var tenantUser = await this._dbContext.TenantUsers
            .FirstOrDefaultAsync(tu => tu.Id == tenantUserId);

        if (tenantUser == null)
        {
            return NotFound(new BaseResponse<TenantTeacherDto>
            {
                Message = "Không tìm thấy hồ sơ giáo viên trong cơ sở giáo dục.",
                StatusCode = "404"
            });
        }

        var teacherInfo = await this._dbContext.TenantTeachers
            .FirstOrDefaultAsync(ts => ts.TenantUserId == tenantUser.Id);

        var addresses = await this._dbContext.AddressUsers
            .Where(a => a.Id == tenantUser.CurrentAddressId || a.Id == tenantUser.PermanentAddressId)
            .ToDictionaryAsync(a => a.Id, a => a);

        TenantUserAddress MapAddress(Guid? addressId)
        {
            if (addressId.HasValue && addresses.TryGetValue(addressId.Value, out var addr))
            {
                return new TenantUserAddress
                {
                    Id = addr.Id,
                    DetailAddress = addr.DetailAddress,
                    ProvinceId = addr.ProvinceId,
                    WardId = addr.WardId,
                    Nationality = addr.Nationality
                };
            }
            return null;
        }

        var result = new TenantTeacherDto
        {
            Id = tenantUser.Id,
            UserId = tenantUser.UserId,
            FirstName = tenantUser.FirstName,
            LastName = tenantUser.LastName,
            Gender = tenantUser.Gender,
            Birthday = tenantUser.Birthday,
            Email = tenantUser.Email,
            Religion = tenantUser.Religion,
            CitizenId = tenantUser.CitizenId,
            Ethnicity = tenantUser.Ethnicity,
            CurrentAddress = MapAddress(tenantUser.CurrentAddressId),
            PermanentAddress = MapAddress(tenantUser.PermanentAddressId),
            TenantTeacherId = teacherInfo?.Id ?? Guid.Empty,
            TeacherCode = teacherInfo?.TeacherCode ?? string.Empty,     // sửa
            SubjectGroup = teacherInfo?.SubjectGroup ?? string.Empty,   // sửa
            JobTitle = teacherInfo?.JobTitle ?? string.Empty,           // sửa
            ContractType = teacherInfo?.ContractType,
            TeacherLevel = teacherInfo?.TeacherLevel,              // sửa (nếu enum)
            TeacherStatus = teacherInfo?.TeacherStatus,
            PhoneNumber = tenantUser.PhoneNumber
        };


        return Ok(new BaseResponse<TenantTeacherDto>
        {
            Data = result,
            StatusCode = "200",
            Message = "Success"
        });
    }

    [HttpPost("{id}/upsert")]
    [Authorize(Role.TenantAdmin)]
    public async Task<ActionResult<BaseResponse<object>>> Upsert(long id, [FromBody] UpdateTeacherInfoRequest request)
    {
        var teacherInfo = await _dbContext.TenantTeachers.FirstOrDefaultAsync(t => t.TenantUserId == id);

        if (teacherInfo == null)
        {
            teacherInfo = new TenantTeacher
            {
                Id = Guid.NewGuid(),
                TenantUserId = id,
                TeacherCode = request.TeacherCode,
                SubjectGroup = request.SubjectGroup,
                JobTitle = request.JobTitle,
                ContractType = request.ContractType,
                TeacherLevel = request.TeacherLevel,
                TeacherStatus = request.TeacherStatus,
            };
            _dbContext.TenantTeachers.Add(teacherInfo);
        }
        else
        {
            teacherInfo.TeacherCode = request.TeacherCode;
            teacherInfo.SubjectGroup = request.SubjectGroup;
            teacherInfo.JobTitle = request.JobTitle;
            teacherInfo.ContractType = request.ContractType;
            teacherInfo.TeacherLevel = request.TeacherLevel;
            teacherInfo.TeacherStatus = request.TeacherStatus;
        }

        await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<object>
        {
            Data = null,
            StatusCode = "200",
            Message = "Success"
        });
    }
    /// <summary>
    /// Import teachers from excel file
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("add-range")]
    public async Task<ActionResult<BaseResponse<object>>> ImportTeachers(ImportExcelRequest request)
    {
        if (request.Teachers == null || request.Teachers.Count == 0)
        {
            return BadRequest(new BaseResponse<object>
            {
                Data = null,
                StatusCode = "400",
                Message = "Không có dữ liệu trong file excel."
            });
        }
        var sheetData = new List<object>();

        request.Teachers.ForEach(s =>
        {
            var firstName = s.FirstName;
            var lastName = s.LastName;
            var religion = s.Religion;
            var ethnicity = s.Ethnicity;
            var gender = MapGender(s.Gender);

            var birthDay = !string.IsNullOrWhiteSpace(s.BirthDay) &&
            DateTime.TryParseExact(s.BirthDay, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var birthDate)
                ? birthDate
                : DateTime.Now;

            var phoneNumber = s.PhoneNumber;
            var email = s.Email;
            var citizenId = s.CitizenId;

            var teacherCode = s.TeacherCode;
            var subjectGroup = s.SubjectGroup;
            var statusStr = s.TeacherStatus;
            var teacherStatus = TenantTeacherStatusMethod(statusStr);
            var jobTitle = s.JobTitle;
            var contractTypeStr = s.ContractType;
            var contractType = ContractTypeMethod(contractTypeStr);

            var rowData = new
            {
                FirstName = firstName,
                LastName = lastName,
                CitizenId = citizenId,
                BirthDay = birthDay,
                Religion = religion,
                Ethnicity = ethnicity,
                Gender = gender,
                Email = email,
                PhoneNumber = phoneNumber,
                SubjectGroup = subjectGroup,
                TeacherStatus = teacherStatus,
                TeacherCode = teacherCode,
                JobTitle = jobTitle,
                ContractType = contractType
            };
            sheetData.Add(rowData);
            Console.WriteLine($"[DEBUG] Added rowData with CitizenId: {citizenId}, FirstName: {firstName}, LastName: {lastName}");
        });
        var tenantTeacherDict = sheetData
                .Select(s =>
                {
                    dynamic item = s;
                    return new
                    {
                        CitizenId = (string)item.CitizenId,
                        item.TeacherStatus,
                        item.SubjectGroup,
                        item.TeacherCode,
                        item.JobTitle,
                        item.ContractType
                    };
                })
                .ToDictionary(s => s.CitizenId, s => s);
        var tenantCode = (string)HttpContext.Items["TenantId"];
        var tenant = _dbContext.Tenants.FirstOrDefault(t => t.Code == tenantCode);

        // Debug: Check if user already exists in tenant
        var existingTenantUser = _dbContext.TenantUsers
            .Where(tu => tu.CitizenId == "035204000645" && tu.TenantId == tenant.Id)
            .FirstOrDefault();

        Console.WriteLine($"[DEBUG] Existing TenantUser for CitizenId 035204000645: {(existingTenantUser != null ? "EXISTS" : "NOT EXISTS")}");
        if (existingTenantUser != null)
        {
            Console.WriteLine($"[DEBUG] Existing TenantUser details: Id={existingTenantUser.Id}, FirstName={existingTenantUser.FirstName}, LastName={existingTenantUser.LastName}");
        }

        Console.WriteLine($"[DEBUG] About to call AddMember with {sheetData.Count} items");
        var tenantUser = await _tenantUserService.AddMember(sheetData, tenant.Id, Role.Teacher);
        Console.WriteLine($"[DEBUG] AddMember returned {tenantUser.Count} tenantUsers");

        if (tenantUser.Count > 0)
        {
            Console.WriteLine($"[DEBUG] First tenantUser: CitizenId={tenantUser[0].CitizenId}, FirstName={tenantUser[0].FirstName}");
        }

        await _dbContext.TenantUsers.AddRangeAsync(tenantUser);

        await _dbContext.SaveChangesAsync();

        var listCitizenJustAdded = tenantUser.Select(tu => tu.CitizenId).ToList();

        var tenantTeachers = tenantTeacherDict.Where(t => listCitizenJustAdded.Contains(t.Key)).ToList();

        var listTenantTeachers = new List<TenantTeacher>();

        foreach (var tenantTeacher in tenantTeachers)
        {
            var tenantTeacherInfo = new TenantTeacher
            {
                Id = Guid.NewGuid(),
                TenantUserId = tenantUser.First(tu => tu.CitizenId == tenantTeacher.Key).Id,
                TeacherCode = tenantTeacher.Value.TeacherCode,
                SubjectGroup = tenantTeacher.Value.SubjectGroup,
                TeacherStatus = tenantTeacher.Value.TeacherStatus,
                JobTitle = tenantTeacher.Value.JobTitle,
                ContractType = tenantTeacher.Value.ContractType
            };
            listTenantTeachers.Add(tenantTeacherInfo);
        }

        await _dbContext.TenantTeachers.AddRangeAsync(listTenantTeachers);
        var tenantUserIds = tenantUser.Select(tu => tu.UserId).ToList();
        if (tenantUserIds.Count > 0)
        {
            await _tenancyUserManager.AddUsersRoleToTenantAsync(tenantUserIds, Role.Teacher, tenant.Id);
        }
        await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<object>
        {
            Data = null,
            StatusCode = "200",
            Message = "Success"
        });
    }


    public static Gender? MapGender(string value) => value switch
    {
        "Nam" => Gender.Male,
        "Nữ" => Gender.Female,
        _ => Gender.Other
    };

    public static TenantTeacherStatus? TenantTeacherStatusMethod(string value) => value switch
    {
        "Thôi việc" => TenantTeacherStatus.Inactive,
        "Đang làm việc" => TenantTeacherStatus.Working,
        _ => null
    };

    public static ContractType? ContractTypeMethod(string type) => type switch
    {
        "Biên chế" => ContractType.Permanent,
        "Dịch vụ" => ContractType.Service,
        _ => null
    };
}

